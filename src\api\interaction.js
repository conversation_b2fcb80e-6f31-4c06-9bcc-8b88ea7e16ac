import request from '@/utils/request'

// 点赞
export const likeService = (targetType, targetId) => {
    return request.post('/likes', {
        targetType,
        targetId
    })
}

// 取消点赞
export const unlikeService = (targetType, targetId) => {
    return request.delete('/likes', {
        data: {
            targetType,
            targetId
        }
    })
}

// 发表评论
export const createCommentService = (contentType, contentId, commentData) => {
    return request.post(`/${contentType}/${contentId}/comments`, commentData)
}

// 收藏文章
export const collectArticleService = (articleId) => {
    return request.post('/collections', {
        articleId
    })
}

// 取消收藏
export const uncollectArticleService = (articleId) => {
    return request.delete(`/collections/${articleId}`)
}

// 关注用户
export const followUserService = (followedUserId) => {
    return request.post('/follows', {
        followedUserId
    })
}

// 取消关注
export const unfollowUserService = (followedUserId) => {
    return request.delete(`/follows/${followedUserId}`)
}

// 投票
export const voteService = (pollId, optionIndex) => {
    return request.post(`/polls/${pollId}/vote`, {
        optionIndex
    })
}
