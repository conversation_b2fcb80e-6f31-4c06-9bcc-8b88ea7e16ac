// Mock数据和API响应
export const mockUsers = [
    {
        userId: 1,
        nickname: '职场萌新',
        email: '<EMAIL>',
        avatarUrl: null,
        bio: '一个热爱分享的开发者',
        followingCount: 15,
        followerCount: 128,
        registrationTime: '2025-01-01T10:00:00'
    },
    {
        userId: 2,
        nickname: '技术大牛',
        email: '<EMAIL>',
        avatarUrl: null,
        bio: '十年磨一剑，专注前端开发',
        followingCount: 50,
        followerCount: 1200,
        registrationTime: '2024-06-15T08:30:00'
    }
]

export const mockArticles = [
    {
        id: 1,
        type: 'ARTICLE',
        title: '前端开发最佳实践分享',
        content: '<p>在前端开发中，我们经常会遇到各种挑战。今天我想分享一些我在实际项目中总结的最佳实践...</p>',
        author: mockUsers[0],
        publishTime: '2025-01-01T14:30:00',
        likeCount: 25,
        commentCount: 8,
        viewCount: 156,
        isLiked: false,
        isCollected: false,
        topics: ['前端开发', '最佳实践'],
        coverImageUrl: null,
        imageUrls: []
    },
    {
        id: 2,
        type: 'DYNAMIC',
        title: null,
        content: '今天学习了Vue 3的新特性，Composition API真的很强大！分享几张学习笔记的截图。',
        author: mockUsers[1],
        publishTime: '2025-01-01T16:45:00',
        likeCount: 12,
        commentCount: 3,
        viewCount: null,
        isLiked: false,
        isCollected: false,
        topics: ['Vue3', '学习笔记'],
        coverImageUrl: null,
        imageUrls: [
            'https://picsum.photos/400/300?random=1',
            'https://picsum.photos/400/300?random=2'
        ]
    }
]

export const mockComments = [
    {
        id: 1,
        author: mockUsers[1],
        commentText: '写得很好，学到了很多！',
        createTime: '2025-01-01T15:00:00',
        likeCount: 3,
        isLiked: false,
        replies: [
            {
                id: 11,
                author: mockUsers[0],
                commentText: '谢谢支持！',
                createTime: '2025-01-01T15:30:00',
                likeCount: 1,
                isLiked: false
            }
        ]
    }
]

// Mock API响应函数
export const createMockResponse = (data, code = 200, message = '操作成功') => {
    return Promise.resolve({
        data: {
            code,
            message,
            data
        }
    })
}

export const createMockPageResponse = (content, page = 0, size = 10, total = null) => {
    const totalElements = total || content.length
    const totalPages = Math.ceil(totalElements / size)
    
    return createMockResponse({
        content,
        totalPages,
        totalElements,
        page,
        size
    })
}

// 模拟延迟
export const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))
