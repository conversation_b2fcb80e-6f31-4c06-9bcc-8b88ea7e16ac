<template>
    <div class="user-list" v-loading="loading">
        <div class="user-item" v-for="user in users" :key="user.userId">
            <el-avatar :src="user.avatarUrl || defaultAvatar" :size="40" />
            <div class="user-info">
                <div class="username" @click="goToProfile(user.userId)">
                    {{ user.nickname }}
                </div>
                <div class="user-bio">{{ user.bio || '这个人很懒，什么都没写...' }}</div>
            </div>
            <el-button
                v-if="tokenStore.token && user.userId !== userInfoStore.info.userId"
                type="primary"
                size="small"
                :loading="user.followLoading"
                @click="handleFollow(user)"
            >
                {{ user.isFollowing ? '已关注' : '关注' }}
            </el-button>
        </div>

        <!-- 加载更多 -->
        <div class="load-more" v-if="hasMore">
            <el-button @click="loadMore" :loading="loadingMore">
                加载更多
            </el-button>
        </div>

        <!-- 空状态 -->
        <el-empty v-if="!loading && users.length === 0" description="暂无用户" />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTokenStore } from '@/stores/token.js'
import { useUserInfoStore } from '@/stores/userInfo.js'
import { getUserFollowingService, getUserFollowersService } from '@/api/user.js'
import { followUserService, unfollowUserService } from '@/api/interaction.js'
import { ElMessage } from 'element-plus'
import defaultAvatar from '@/assets/default.png'

// Props
const props = defineProps({
    userId: {
        type: [String, Number],
        required: true
    },
    type: {
        type: String,
        required: true, // 'following' 或 'followers'
        validator: (value) => ['following', 'followers'].includes(value)
    }
})

const router = useRouter()
const tokenStore = useTokenStore()
const userInfoStore = useUserInfoStore()

// 响应式数据
const users = ref([])
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const currentPage = ref(0)
const pageSize = ref(20)

// 方法
const loadUsers = async (reset = false) => {
    if (reset) {
        currentPage.value = 0
        users.value = []
        hasMore.value = true
    }
    
    if (!hasMore.value) return
    
    try {
        loading.value = reset
        loadingMore.value = !reset
        
        let result
        if (props.type === 'following') {
            result = await getUserFollowingService(props.userId, currentPage.value, pageSize.value)
        } else {
            result = await getUserFollowersService(props.userId, currentPage.value, pageSize.value)
        }
        
        const newUsers = result.data.content || []
        
        if (reset) {
            users.value = newUsers
        } else {
            users.value.push(...newUsers)
        }
        
        hasMore.value = currentPage.value < (result.data.totalPages - 1)
        currentPage.value++
        
    } catch (error) {
        ElMessage.error('加载用户列表失败')
        console.error('加载用户列表失败:', error)
    } finally {
        loading.value = false
        loadingMore.value = false
    }
}

const loadMore = () => {
    loadUsers(false)
}

const handleFollow = async (user) => {
    if (!tokenStore.token) {
        ElMessage.warning('请先登录')
        return
    }
    
    try {
        user.followLoading = true
        if (user.isFollowing) {
            await unfollowUserService(user.userId)
        } else {
            await followUserService(user.userId)
        }
        user.isFollowing = !user.isFollowing
        ElMessage.success(user.isFollowing ? '关注成功' : '取消关注')
    } catch (error) {
        ElMessage.error('操作失败')
    } finally {
        user.followLoading = false
    }
}

const goToProfile = (userId) => {
    router.push(`/users/${userId}`)
}

// 生命周期
onMounted(() => {
    loadUsers(true)
})
</script>

<style lang="scss" scoped>
.user-list {
    .user-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #f0f2f5;

        &:last-child {
            border-bottom: none;
        }

        .user-info {
            flex: 1;
            margin-left: 12px;

            .username {
                font-weight: 500;
                color: #303133;
                cursor: pointer;
                margin-bottom: 4px;
                
                &:hover {
                    color: #409eff;
                }
            }

            .user-bio {
                font-size: 12px;
                color: #909399;
                line-height: 1.4;
                
                // 限制显示行数
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }
        }
    }

    .load-more {
        text-align: center;
        margin: 20px 0;
    }
}
</style>
