{"name": "big_event_vue", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@vueup/vue-quill": "^1.2.0", "axios": "^1.7.9", "element-plus": "^2.9.3", "pinia": "^2.3.1", "pinia-persistedstate-plugin": "^0.1.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "sass": "^1.83.4", "vite": "^6.0.11", "vite-plugin-vue-devtools": "^7.7.0"}}