<template>
    <div class="home-container">
        <!-- 发布快捷入口 -->
        <div class="publish-card" v-if="tokenStore.token">
            <div class="publish-header">
                <el-avatar :src="userInfoStore.info.avatarUrl || defaultAvatar" :size="40" />
                <el-input
                    placeholder="分享你的想法..."
                    class="publish-input"
                    readonly
                    @click="showPublishDialog = true"
                />
            </div>
            <div class="publish-actions">
                <el-button type="text" :icon="Picture" @click="showPublishDialog = true">
                    图片
                </el-button>
                <el-button type="text" :icon="VideoCamera" @click="showPublishDialog = true">
                    视频
                </el-button>
                <el-button type="text" :icon="ChatDotRound" @click="showPublishDialog = true">
                    话题
                </el-button>
            </div>
        </div>

        <!-- 内容筛选标签 -->
        <div class="filter-tabs">
            <el-tabs v-model="activeTab" @tab-change="handleTabChange">
                <el-tab-pane label="推荐" name="recommend"></el-tab-pane>
                <el-tab-pane label="最新" name="latest"></el-tab-pane>
                <el-tab-pane label="热门" name="hot"></el-tab-pane>
                <el-tab-pane label="关注" name="following" v-if="tokenStore.token"></el-tab-pane>
            </el-tabs>
        </div>

        <!-- 信息流列表 -->
        <div class="feed-list" v-loading="loading">
            <div
                v-for="item in feedList"
                :key="item.id"
                class="feed-item"
                @click="handleItemClick(item)"
            >
                <!-- 用户信息 -->
                <div class="user-info">
                    <el-avatar :src="item.author.avatarUrl || defaultAvatar" :size="40" />
                    <div class="user-details">
                        <div class="username">{{ item.author.nickname }}</div>
                        <div class="publish-time">{{ formatTime(item.publishTime) }}</div>
                    </div>
                    <el-button
                        v-if="tokenStore.token && item.author.userId !== userInfoStore.info.userId"
                        type="primary"
                        size="small"
                        :loading="item.followLoading"
                        @click.stop="handleFollow(item)"
                    >
                        {{ item.author.isFollowing ? '已关注' : '关注' }}
                    </el-button>
                </div>

                <!-- 内容区域 -->
                <div class="content-area">
                    <!-- 文章标题 -->
                    <h3 v-if="item.type === 'ARTICLE'" class="article-title">
                        {{ item.title }}
                    </h3>
                    
                    <!-- 内容文本 -->
                    <div class="content-text" v-html="item.content"></div>
                    
                    <!-- 图片展示 -->
                    <div v-if="item.imageUrls && item.imageUrls.length > 0" class="image-gallery">
                        <el-image
                            v-for="(img, index) in item.imageUrls.slice(0, 9)"
                            :key="index"
                            :src="img"
                            :preview-src-list="item.imageUrls"
                            :initial-index="index"
                            class="gallery-image"
                            fit="cover"
                        />
                    </div>
                    
                    <!-- 话题标签 -->
                    <div v-if="item.topics && item.topics.length > 0" class="topics">
                        <el-tag
                            v-for="topic in item.topics"
                            :key="topic"
                            size="small"
                            type="info"
                            class="topic-tag"
                            @click.stop="handleTopicClick(topic)"
                        >
                            #{{ topic }}
                        </el-tag>
                    </div>
                    
                    <!-- 投票组件 -->
                    <div v-if="item.poll" class="poll-section">
                        <div class="poll-question">{{ item.poll.question }}</div>
                        <div class="poll-options">
                            <div
                                v-for="(option, index) in item.poll.options"
                                :key="index"
                                class="poll-option"
                                :class="{ voted: item.poll.userVoted === index }"
                                @click.stop="handleVote(item, index)"
                            >
                                <div class="option-text">{{ option.text }}</div>
                                <div class="option-progress">
                                    <div
                                        class="progress-bar"
                                        :style="{ width: option.percentage + '%' }"
                                    ></div>
                                    <span class="percentage">{{ option.percentage }}%</span>
                                </div>
                            </div>
                        </div>
                        <div class="poll-stats">{{ item.poll.totalVotes }} 人参与投票</div>
                    </div>
                </div>

                <!-- 互动区域 -->
                <div class="interaction-area">
                    <div class="interaction-buttons">
                        <el-button
                            type="text"
                            :icon="item.isLiked ? StarFilled : Star"
                            :class="{ liked: item.isLiked }"
                            @click.stop="handleLike(item)"
                        >
                            {{ item.likeCount || 0 }}
                        </el-button>
                        
                        <el-button
                            type="text"
                            :icon="ChatDotRound"
                            @click.stop="handleComment(item)"
                        >
                            {{ item.commentCount || 0 }}
                        </el-button>
                        
                        <el-button
                            v-if="item.type === 'ARTICLE'"
                            type="text"
                            :icon="item.isCollected ? StarFilled : Star"
                            :class="{ collected: item.isCollected }"
                            @click.stop="handleCollect(item)"
                        >
                            {{ item.isCollected ? '已收藏' : '收藏' }}
                        </el-button>
                        
                        <el-button type="text" :icon="Share">
                            分享
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载更多 -->
        <div class="load-more" v-if="hasMore">
            <el-button @click="loadMore" :loading="loadingMore">
                加载更多
            </el-button>
        </div>

        <!-- 没有更多内容 -->
        <div v-else-if="feedList.length > 0" class="no-more">
            没有更多内容了
        </div>

        <!-- 空状态 -->
        <el-empty v-if="!loading && feedList.length === 0" description="暂无内容" />
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useTokenStore } from '@/stores/token.js'
import { useUserInfoStore } from '@/stores/userInfo.js'
import { getFeedService } from '@/api/content.js'
import { likeService, unlikeService, followUserService, unfollowUserService, collectArticleService, uncollectArticleService, voteService } from '@/api/interaction.js'
import { ElMessage } from 'element-plus'
import {
    Picture,
    VideoCamera,
    ChatDotRound,
    Star,
    StarFilled,
    Share
} from '@element-plus/icons-vue'
import defaultAvatar from '@/assets/default.png'

const router = useRouter()
const tokenStore = useTokenStore()
const userInfoStore = useUserInfoStore()

// 响应式数据
const activeTab = ref('recommend')
const feedList = ref([])
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const currentPage = ref(0)
const pageSize = ref(10)
const showPublishDialog = ref(false)

// 计算属性
const isLoggedIn = computed(() => !!tokenStore.token)

// 方法
const loadFeed = async (reset = false) => {
    if (reset) {
        currentPage.value = 0
        feedList.value = []
        hasMore.value = true
    }
    
    if (!hasMore.value) return
    
    try {
        loading.value = reset
        loadingMore.value = !reset
        
        const result = await getFeedService(currentPage.value, pageSize.value)
        const newItems = result.data.content || []
        
        if (reset) {
            feedList.value = newItems
        } else {
            feedList.value.push(...newItems)
        }
        
        hasMore.value = currentPage.value < (result.data.totalPages - 1)
        currentPage.value++
        
    } catch (error) {
        ElMessage.error('加载失败')
        console.error('加载信息流失败:', error)
    } finally {
        loading.value = false
        loadingMore.value = false
    }
}

const loadMore = () => {
    loadFeed(false)
}

const handleTabChange = (tab) => {
    activeTab.value = tab
    loadFeed(true)
}

const handleItemClick = (item) => {
    if (item.type === 'ARTICLE') {
        router.push(`/articles/${item.id}`)
    } else {
        router.push(`/dynamics/${item.id}`)
    }
}

const handleLike = async (item) => {
    if (!isLoggedIn.value) {
        ElMessage.warning('请先登录')
        return
    }
    
    try {
        if (item.isLiked) {
            await unlikeService(item.type, item.id)
            item.likeCount--
        } else {
            await likeService(item.type, item.id)
            item.likeCount++
        }
        item.isLiked = !item.isLiked
    } catch (error) {
        ElMessage.error('操作失败')
    }
}

const handleComment = (item) => {
    // 跳转到详情页的评论区
    handleItemClick(item)
}

const handleCollect = async (item) => {
    if (!isLoggedIn.value) {
        ElMessage.warning('请先登录')
        return
    }
    
    try {
        if (item.isCollected) {
            await uncollectArticleService(item.id)
        } else {
            await collectArticleService(item.id)
        }
        item.isCollected = !item.isCollected
        ElMessage.success(item.isCollected ? '收藏成功' : '取消收藏')
    } catch (error) {
        ElMessage.error('操作失败')
    }
}

const handleFollow = async (item) => {
    if (!isLoggedIn.value) {
        ElMessage.warning('请先登录')
        return
    }
    
    try {
        item.followLoading = true
        if (item.author.isFollowing) {
            await unfollowUserService(item.author.userId)
        } else {
            await followUserService(item.author.userId)
        }
        item.author.isFollowing = !item.author.isFollowing
        ElMessage.success(item.author.isFollowing ? '关注成功' : '取消关注')
    } catch (error) {
        ElMessage.error('操作失败')
    } finally {
        item.followLoading = false
    }
}

const handleVote = async (item, optionIndex) => {
    if (!isLoggedIn.value) {
        ElMessage.warning('请先登录')
        return
    }
    
    if (item.poll.userVoted !== null) {
        ElMessage.warning('您已经投过票了')
        return
    }
    
    try {
        await voteService(item.poll.id, optionIndex)
        item.poll.userVoted = optionIndex
        // 这里应该重新获取投票结果，简化处理
        ElMessage.success('投票成功')
    } catch (error) {
        ElMessage.error('投票失败')
    }
}

const handleTopicClick = (topic) => {
    router.push(`/topics/${topic}`)
}

const formatTime = (time) => {
    // 简单的时间格式化，可以使用 dayjs 等库
    return new Date(time).toLocaleString()
}

// 生命周期
onMounted(() => {
    loadFeed(true)
})
</script>

<style lang="scss" scoped>
.home-container {
    max-width: 600px;
    margin: 0 auto;

    .publish-card {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .publish-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;

            .publish-input {
                flex: 1;

                :deep(.el-input__inner) {
                    cursor: pointer;
                    background-color: #f5f7fa;
                    border: none;
                }
            }
        }

        .publish-actions {
            display: flex;
            gap: 20px;
            padding-left: 52px;

            .el-button {
                padding: 0;
                color: #909399;

                &:hover {
                    color: #409eff;
                }
            }
        }
    }

    .filter-tabs {
        background: #fff;
        border-radius: 8px;
        margin-bottom: 20px;
        padding: 0 20px;

        :deep(.el-tabs__header) {
            margin: 0;
        }

        :deep(.el-tabs__nav-wrap::after) {
            display: none;
        }
    }

    .feed-list {
        .feed-item {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

            &:hover {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            }

            .user-info {
                display: flex;
                align-items: center;
                margin-bottom: 15px;

                .user-details {
                    flex: 1;
                    margin-left: 12px;

                    .username {
                        font-weight: 500;
                        color: #303133;
                        margin-bottom: 4px;
                    }

                    .publish-time {
                        font-size: 12px;
                        color: #909399;
                    }
                }
            }

            .content-area {
                .article-title {
                    font-size: 18px;
                    font-weight: 600;
                    color: #303133;
                    margin: 0 0 12px;
                    line-height: 1.4;
                }

                .content-text {
                    color: #606266;
                    line-height: 1.6;
                    margin-bottom: 15px;

                    // 限制显示行数
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }

                .image-gallery {
                    display: grid;
                    gap: 8px;
                    margin-bottom: 15px;

                    &:has(.gallery-image:nth-child(1):nth-last-child(1)) {
                        grid-template-columns: 1fr;
                    }

                    &:has(.gallery-image:nth-child(2)) {
                        grid-template-columns: 1fr 1fr;
                    }

                    &:has(.gallery-image:nth-child(3)) {
                        grid-template-columns: repeat(3, 1fr);
                    }

                    .gallery-image {
                        width: 100%;
                        height: 120px;
                        border-radius: 6px;
                        cursor: pointer;
                    }
                }

                .topics {
                    margin-bottom: 15px;

                    .topic-tag {
                        margin-right: 8px;
                        margin-bottom: 4px;
                        cursor: pointer;

                        &:hover {
                            background-color: #409eff;
                            color: #fff;
                        }
                    }
                }

                .poll-section {
                    border: 1px solid #e4e7ed;
                    border-radius: 6px;
                    padding: 15px;
                    margin-bottom: 15px;

                    .poll-question {
                        font-weight: 500;
                        margin-bottom: 12px;
                        color: #303133;
                    }

                    .poll-options {
                        .poll-option {
                            margin-bottom: 8px;
                            cursor: pointer;

                            &.voted {
                                .option-text {
                                    color: #409eff;
                                    font-weight: 500;
                                }
                            }

                            .option-text {
                                margin-bottom: 4px;
                                color: #606266;
                            }

                            .option-progress {
                                position: relative;
                                height: 20px;
                                background-color: #f5f7fa;
                                border-radius: 10px;
                                overflow: hidden;

                                .progress-bar {
                                    height: 100%;
                                    background-color: #409eff;
                                    transition: width 0.3s;
                                }

                                .percentage {
                                    position: absolute;
                                    right: 8px;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    font-size: 12px;
                                    color: #909399;
                                }
                            }
                        }
                    }

                    .poll-stats {
                        font-size: 12px;
                        color: #909399;
                        margin-top: 8px;
                    }
                }
            }

            .interaction-area {
                border-top: 1px solid #f0f2f5;
                padding-top: 12px;

                .interaction-buttons {
                    display: flex;
                    gap: 20px;

                    .el-button {
                        padding: 0;
                        color: #909399;

                        &:hover {
                            color: #409eff;
                        }

                        &.liked {
                            color: #f56c6c;
                        }

                        &.collected {
                            color: #e6a23c;
                        }
                    }
                }
            }
        }
    }

    .load-more {
        text-align: center;
        margin: 20px 0;
    }

    .no-more {
        text-align: center;
        color: #909399;
        font-size: 14px;
        margin: 20px 0;
    }
}
</style>
