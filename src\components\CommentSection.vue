<template>
    <div class="comment-section">
        <!-- 评论输入框 -->
        <div class="comment-input" v-if="tokenStore.token">
            <el-avatar :src="userInfoStore.info.avatarUrl || defaultAvatar" :size="32" />
            <div class="input-area">
                <el-input
                    v-model="commentText"
                    type="textarea"
                    :rows="3"
                    placeholder="写下你的评论..."
                    maxlength="500"
                    show-word-limit
                />
                <div class="input-actions">
                    <el-button @click="clearComment">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="submitComment"
                        :disabled="!commentText.trim()"
                        :loading="submitting"
                    >
                        发布
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 评论列表 -->
        <div class="comment-list" v-loading="loading">
            <div 
                v-for="comment in comments" 
                :key="comment.id" 
                class="comment-item"
            >
                <!-- 一级评论 -->
                <div class="comment-main">
                    <el-avatar :src="comment.author.avatarUrl || defaultAvatar" :size="36" />
                    <div class="comment-content">
                        <div class="comment-header">
                            <span class="author-name">{{ comment.author.nickname }}</span>
                            <span class="comment-time">{{ formatTime(comment.createTime) }}</span>
                        </div>
                        <div class="comment-text">{{ comment.commentText }}</div>
                        <div class="comment-actions">
                            <el-button 
                                type="text" 
                                size="small"
                                :icon="comment.isLiked ? StarFilled : Star"
                                :class="{ liked: comment.isLiked }"
                                @click="handleLikeComment(comment)"
                            >
                                {{ comment.likeCount || 0 }}
                            </el-button>
                            <el-button 
                                type="text" 
                                size="small"
                                @click="showReplyInput(comment)"
                            >
                                回复
                            </el-button>
                        </div>
                    </div>
                </div>

                <!-- 回复输入框 -->
                <div v-if="replyingTo === comment.id" class="reply-input">
                    <el-input
                        v-model="replyText"
                        type="textarea"
                        :rows="2"
                        :placeholder="`回复 @${comment.author.nickname}:`"
                        maxlength="300"
                    />
                    <div class="reply-actions">
                        <el-button size="small" @click="cancelReply">取消</el-button>
                        <el-button 
                            size="small" 
                            type="primary" 
                            @click="submitReply(comment)"
                            :disabled="!replyText.trim()"
                            :loading="replySubmitting"
                        >
                            回复
                        </el-button>
                    </div>
                </div>

                <!-- 二级回复列表 -->
                <div v-if="comment.replies && comment.replies.length > 0" class="replies">
                    <div 
                        v-for="reply in comment.replies" 
                        :key="reply.id" 
                        class="reply-item"
                    >
                        <el-avatar :src="reply.author.avatarUrl || defaultAvatar" :size="28" />
                        <div class="reply-content">
                            <div class="reply-header">
                                <span class="author-name">{{ reply.author.nickname }}</span>
                                <span class="reply-time">{{ formatTime(reply.createTime) }}</span>
                            </div>
                            <div class="reply-text">{{ reply.commentText }}</div>
                            <div class="reply-actions">
                                <el-button 
                                    type="text" 
                                    size="small"
                                    :icon="reply.isLiked ? StarFilled : Star"
                                    :class="{ liked: reply.isLiked }"
                                    @click="handleLikeComment(reply)"
                                >
                                    {{ reply.likeCount || 0 }}
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载更多评论 -->
        <div class="load-more" v-if="hasMore">
            <el-button @click="loadMoreComments" :loading="loadingMore">
                加载更多评论
            </el-button>
        </div>

        <!-- 空状态 -->
        <el-empty v-if="!loading && comments.length === 0" description="暂无评论" />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useTokenStore } from '@/stores/token.js'
import { useUserInfoStore } from '@/stores/userInfo.js'
import { createCommentService } from '@/api/interaction.js'
import { likeService, unlikeService } from '@/api/interaction.js'
import { ElMessage } from 'element-plus'
import { Star, StarFilled } from '@element-plus/icons-vue'
import defaultAvatar from '@/assets/default.png'

// Props
const props = defineProps({
    contentType: {
        type: String,
        required: true // 'articles' 或 'dynamics'
    },
    contentId: {
        type: [String, Number],
        required: true
    }
})

// Stores
const tokenStore = useTokenStore()
const userInfoStore = useUserInfoStore()

// 响应式数据
const comments = ref([])
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const currentPage = ref(0)

const commentText = ref('')
const submitting = ref(false)

const replyingTo = ref(null)
const replyText = ref('')
const replySubmitting = ref(false)

// 方法
const loadComments = async (reset = false) => {
    if (reset) {
        currentPage.value = 0
        comments.value = []
        hasMore.value = true
    }
    
    if (!hasMore.value) return
    
    try {
        loading.value = reset
        loadingMore.value = !reset
        
        // 这里应该调用获取评论的API，暂时使用模拟数据
        const mockComments = [
            {
                id: 1,
                author: {
                    userId: 2,
                    nickname: '用户A',
                    avatarUrl: null
                },
                commentText: '这篇文章写得很好！',
                createTime: new Date().toISOString(),
                likeCount: 5,
                isLiked: false,
                replies: [
                    {
                        id: 11,
                        author: {
                            userId: 3,
                            nickname: '用户B',
                            avatarUrl: null
                        },
                        commentText: '同意！',
                        createTime: new Date().toISOString(),
                        likeCount: 2,
                        isLiked: false
                    }
                ]
            }
        ]
        
        if (reset) {
            comments.value = mockComments
        } else {
            comments.value.push(...mockComments)
        }
        
        hasMore.value = false // 模拟数据只有一页
        currentPage.value++
        
    } catch (error) {
        ElMessage.error('加载评论失败')
        console.error('加载评论失败:', error)
    } finally {
        loading.value = false
        loadingMore.value = false
    }
}

const loadMoreComments = () => {
    loadComments(false)
}

const submitComment = async () => {
    if (!commentText.value.trim()) return
    
    try {
        submitting.value = true
        await createCommentService(props.contentType, props.contentId, {
            commentText: commentText.value
        })
        
        ElMessage.success('评论发布成功')
        commentText.value = ''
        loadComments(true) // 重新加载评论列表
        
    } catch (error) {
        ElMessage.error('评论发布失败')
        console.error('评论发布失败:', error)
    } finally {
        submitting.value = false
    }
}

const clearComment = () => {
    commentText.value = ''
}

const showReplyInput = (comment) => {
    replyingTo.value = comment.id
    replyText.value = ''
}

const cancelReply = () => {
    replyingTo.value = null
    replyText.value = ''
}

const submitReply = async (parentComment) => {
    if (!replyText.value.trim()) return
    
    try {
        replySubmitting.value = true
        await createCommentService(props.contentType, props.contentId, {
            commentText: replyText.value,
            parentCommentId: parentComment.id
        })
        
        ElMessage.success('回复发布成功')
        cancelReply()
        loadComments(true) // 重新加载评论列表
        
    } catch (error) {
        ElMessage.error('回复发布失败')
        console.error('回复发布失败:', error)
    } finally {
        replySubmitting.value = false
    }
}

const handleLikeComment = async (comment) => {
    if (!tokenStore.token) {
        ElMessage.warning('请先登录')
        return
    }
    
    try {
        if (comment.isLiked) {
            await unlikeService('COMMENT', comment.id)
            comment.likeCount--
        } else {
            await likeService('COMMENT', comment.id)
            comment.likeCount++
        }
        comment.isLiked = !comment.isLiked
    } catch (error) {
        ElMessage.error('操作失败')
    }
}

const formatTime = (time) => {
    return new Date(time).toLocaleString()
}

// 生命周期
onMounted(() => {
    loadComments(true)
})
</script>

<style lang="scss" scoped>
.comment-section {
    .comment-input {
        display: flex;
        gap: 12px;
        margin-bottom: 24px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;

        .input-area {
            flex: 1;

            .input-actions {
                display: flex;
                justify-content: flex-end;
                gap: 8px;
                margin-top: 12px;
            }
        }
    }

    .comment-list {
        .comment-item {
            margin-bottom: 20px;

            .comment-main {
                display: flex;
                gap: 12px;

                .comment-content {
                    flex: 1;

                    .comment-header {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        margin-bottom: 8px;

                        .author-name {
                            font-weight: 500;
                            color: #303133;
                        }

                        .comment-time {
                            font-size: 12px;
                            color: #909399;
                        }
                    }

                    .comment-text {
                        color: #606266;
                        line-height: 1.6;
                        margin-bottom: 8px;
                    }

                    .comment-actions {
                        display: flex;
                        gap: 16px;

                        .el-button {
                            padding: 0;
                            color: #909399;

                            &:hover {
                                color: #409eff;
                            }

                            &.liked {
                                color: #f56c6c;
                            }
                        }
                    }
                }
            }

            .reply-input {
                margin: 12px 0 12px 48px;
                padding: 12px;
                background: #f8f9fa;
                border-radius: 6px;

                .reply-actions {
                    display: flex;
                    justify-content: flex-end;
                    gap: 8px;
                    margin-top: 8px;
                }
            }

            .replies {
                margin-left: 48px;
                margin-top: 12px;
                padding-left: 16px;
                border-left: 2px solid #e4e7ed;

                .reply-item {
                    display: flex;
                    gap: 8px;
                    margin-bottom: 12px;

                    .reply-content {
                        flex: 1;

                        .reply-header {
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            margin-bottom: 4px;

                            .author-name {
                                font-weight: 500;
                                color: #303133;
                                font-size: 14px;
                            }

                            .reply-time {
                                font-size: 11px;
                                color: #909399;
                            }
                        }

                        .reply-text {
                            color: #606266;
                            font-size: 14px;
                            line-height: 1.5;
                            margin-bottom: 4px;
                        }

                        .reply-actions {
                            .el-button {
                                padding: 0;
                                color: #909399;
                                font-size: 12px;

                                &:hover {
                                    color: #409eff;
                                }

                                &.liked {
                                    color: #f56c6c;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .load-more {
        text-align: center;
        margin: 20px 0;
    }
}
</style>
