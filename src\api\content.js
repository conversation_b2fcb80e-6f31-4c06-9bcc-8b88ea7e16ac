import request from '@/utils/request'

// 获取首页信息流
export const getFeedService = (page = 0, size = 10) => {
    return request.get('/feed', {
        params: { page, size }
    })
}

// 创建文章
export const createArticleService = (articleData) => {
    return request.post('/articles', articleData)
}

// 获取文章详情
export const getArticleDetailService = (id) => {
    return request.get(`/articles/${id}`)
}

// 创建动态
export const createDynamicService = (dynamicData) => {
    return request.post('/dynamics', dynamicData)
}

// 搜索内容
export const searchContentService = (keyword, page = 0, size = 10) => {
    return request.get('/search', {
        params: { q: keyword, page, size }
    })
}

// 获取热门话题
export const getHotTopicsService = (limit = 10) => {
    return request.get('/topics/hot', {
        params: { limit }
    })
}

// 获取话题下的内容
export const getTopicContentService = (topicName, page = 0, size = 10) => {
    return request.get(`/topics/${topicName}/content`, {
        params: { page, size }
    })
}

// 搜索话题
export const searchTopicsService = (keyword) => {
    return request.get('/topics/search', {
        params: { q: keyword }
    })
}
