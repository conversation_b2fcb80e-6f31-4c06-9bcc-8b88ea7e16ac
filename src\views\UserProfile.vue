<template>
    <div class="user-profile" v-loading="loading">
        <div v-if="userProfile" class="profile-container">
            <!-- 用户信息卡片 -->
            <div class="user-card">
                <div class="user-header">
                    <el-avatar :src="userProfile.avatarUrl || defaultAvatar" :size="80" />
                    <div class="user-info">
                        <h2 class="username">{{ userProfile.nickname }}</h2>
                        <p class="user-bio">{{ userProfile.bio || '这个人很懒，什么都没写...' }}</p>
                        <div class="user-stats">
                            <div class="stat-item" @click="showFollowing = true">
                                <span class="stat-number">{{ userProfile.followingCount || 0 }}</span>
                                <span class="stat-label">关注</span>
                            </div>
                            <div class="stat-item" @click="showFollowers = true">
                                <span class="stat-number">{{ userProfile.followerCount || 0 }}</span>
                                <span class="stat-label">粉丝</span>
                            </div>
                        </div>
                    </div>
                    <div class="user-actions" v-if="!userProfile.isMe">
                        <el-button
                            v-if="tokenStore.token"
                            type="primary"
                            :loading="followLoading"
                            @click="handleFollow"
                        >
                            {{ userProfile.isFollowing ? '已关注' : '关注' }}
                        </el-button>
                        <el-button :icon="ChatDotRound">私信</el-button>
                    </div>
                    <div class="user-actions" v-else>
                        <el-button @click="$router.push('/user/info')">编辑资料</el-button>
                    </div>
                </div>
            </div>

            <!-- 内容标签页 -->
            <div class="content-tabs">
                <el-tabs v-model="activeTab" @tab-change="handleTabChange">
                    <el-tab-pane label="动态" name="all"></el-tab-pane>
                    <el-tab-pane label="文章" name="articles"></el-tab-pane>
                    <el-tab-pane label="收藏" name="collections" v-if="userProfile.isMe"></el-tab-pane>
                </el-tabs>
            </div>

            <!-- 内容列表 -->
            <div class="content-list" v-loading="contentLoading">
                <div
                    v-for="item in contentList"
                    :key="item.id"
                    class="content-item"
                    @click="handleItemClick(item)"
                >
                    <!-- 内容项展示，复用Home页面的样式 -->
                    <div class="content-header">
                        <div class="content-type">
                            <el-tag v-if="item.type === 'ARTICLE'" type="primary" size="small">文章</el-tag>
                            <el-tag v-else type="success" size="small">动态</el-tag>
                        </div>
                        <div class="publish-time">{{ formatTime(item.publishTime) }}</div>
                    </div>

                    <h3 v-if="item.type === 'ARTICLE'" class="content-title">
                        {{ item.title }}
                    </h3>
                    
                    <div class="content-text" v-html="item.content"></div>
                    
                    <!-- 图片展示 -->
                    <div v-if="item.imageUrls && item.imageUrls.length > 0" class="image-gallery">
                        <el-image
                            v-for="(img, index) in item.imageUrls.slice(0, 3)"
                            :key="index"
                            :src="img"
                            :preview-src-list="item.imageUrls"
                            :initial-index="index"
                            class="gallery-image"
                            fit="cover"
                        />
                    </div>
                    
                    <!-- 话题标签 -->
                    <div v-if="item.topics && item.topics.length > 0" class="topics">
                        <el-tag
                            v-for="topic in item.topics"
                            :key="topic"
                            size="small"
                            type="info"
                            class="topic-tag"
                            @click.stop="handleTopicClick(topic)"
                        >
                            #{{ topic }}
                        </el-tag>
                    </div>

                    <!-- 互动数据 -->
                    <div class="interaction-stats">
                        <span class="stat">
                            <el-icon><Star /></el-icon>
                            {{ item.likeCount || 0 }}
                        </span>
                        <span class="stat">
                            <el-icon><ChatDotRound /></el-icon>
                            {{ item.commentCount || 0 }}
                        </span>
                        <span v-if="item.type === 'ARTICLE'" class="stat">
                            <el-icon><View /></el-icon>
                            {{ item.viewCount || 0 }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- 加载更多 -->
            <div class="load-more" v-if="hasMore">
                <el-button @click="loadMoreContent" :loading="loadingMore">
                    加载更多
                </el-button>
            </div>

            <!-- 空状态 -->
            <el-empty v-if="!contentLoading && contentList.length === 0" description="暂无内容" />
        </div>

        <!-- 错误状态 -->
        <el-result
            v-else-if="!loading"
            icon="warning"
            title="用户不存在"
            sub-title="该用户可能已注销或不存在"
        >
            <template #extra>
                <el-button type="primary" @click="$router.go(-1)">返回</el-button>
            </template>
        </el-result>

        <!-- 关注列表弹窗 -->
        <el-dialog v-model="showFollowing" title="关注列表" width="500px">
            <UserList :user-id="userProfile?.userId" type="following" />
        </el-dialog>

        <!-- 粉丝列表弹窗 -->
        <el-dialog v-model="showFollowers" title="粉丝列表" width="500px">
            <UserList :user-id="userProfile?.userId" type="followers" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTokenStore } from '@/stores/token.js'
import { useUserInfoStore } from '@/stores/userInfo.js'
import { getUserProfileService } from '@/api/user.js'
import { followUserService, unfollowUserService } from '@/api/interaction.js'
import { ElMessage } from 'element-plus'
import {
    ChatDotRound,
    Star,
    View
} from '@element-plus/icons-vue'
import UserList from '@/components/UserList.vue'
import defaultAvatar from '@/assets/default.png'

const route = useRoute()
const router = useRouter()
const tokenStore = useTokenStore()
const userInfoStore = useUserInfoStore()

// 响应式数据
const userProfile = ref(null)
const loading = ref(false)
const followLoading = ref(false)

const activeTab = ref('all')
const contentList = ref([])
const contentLoading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const currentPage = ref(0)

const showFollowing = ref(false)
const showFollowers = ref(false)

// 计算属性
const userId = computed(() => route.params.id)

// 方法
const loadUserProfile = async () => {
    try {
        loading.value = true
        const result = await getUserProfileService(userId.value, 0, 10)
        userProfile.value = result.data
        contentList.value = result.data.content.content || []
        hasMore.value = currentPage.value < (result.data.content.totalPages - 1)
        currentPage.value = 1
    } catch (error) {
        ElMessage.error('加载用户信息失败')
        console.error('加载用户信息失败:', error)
    } finally {
        loading.value = false
    }
}

const loadContent = async (reset = false) => {
    if (reset) {
        currentPage.value = 0
        contentList.value = []
        hasMore.value = true
    }
    
    if (!hasMore.value) return
    
    try {
        contentLoading.value = reset
        loadingMore.value = !reset
        
        const result = await getUserProfileService(userId.value, currentPage.value, 10)
        const newItems = result.data.content.content || []
        
        if (reset) {
            contentList.value = newItems
        } else {
            contentList.value.push(...newItems)
        }
        
        hasMore.value = currentPage.value < (result.data.content.totalPages - 1)
        currentPage.value++
        
    } catch (error) {
        ElMessage.error('加载内容失败')
        console.error('加载内容失败:', error)
    } finally {
        contentLoading.value = false
        loadingMore.value = false
    }
}

const loadMoreContent = () => {
    loadContent(false)
}

const handleTabChange = (tab) => {
    activeTab.value = tab
    loadContent(true)
}

const handleFollow = async () => {
    if (!tokenStore.token) {
        ElMessage.warning('请先登录')
        return
    }
    
    try {
        followLoading.value = true
        if (userProfile.value.isFollowing) {
            await unfollowUserService(userProfile.value.userId)
            userProfile.value.followerCount--
        } else {
            await followUserService(userProfile.value.userId)
            userProfile.value.followerCount++
        }
        userProfile.value.isFollowing = !userProfile.value.isFollowing
        ElMessage.success(userProfile.value.isFollowing ? '关注成功' : '取消关注')
    } catch (error) {
        ElMessage.error('操作失败')
    } finally {
        followLoading.value = false
    }
}

const handleItemClick = (item) => {
    if (item.type === 'ARTICLE') {
        router.push(`/articles/${item.id}`)
    } else {
        router.push(`/dynamics/${item.id}`)
    }
}

const handleTopicClick = (topic) => {
    router.push(`/topics/${topic}`)
}

const formatTime = (time) => {
    return new Date(time).toLocaleString()
}

// 生命周期
onMounted(() => {
    loadUserProfile()
})
</script>

<style lang="scss" scoped>
.user-profile {
    max-width: 800px;
    margin: 0 auto;

    .profile-container {
        .user-card {
            background: #fff;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

            .user-header {
                display: flex;
                align-items: flex-start;
                gap: 20px;

                .user-info {
                    flex: 1;

                    .username {
                        font-size: 24px;
                        font-weight: 600;
                        color: #303133;
                        margin: 0 0 10px;
                    }

                    .user-bio {
                        color: #606266;
                        line-height: 1.6;
                        margin: 0 0 15px;
                    }

                    .user-stats {
                        display: flex;
                        gap: 30px;

                        .stat-item {
                            text-align: center;
                            cursor: pointer;
                            transition: all 0.2s;

                            &:hover {
                                transform: translateY(-2px);
                            }

                            .stat-number {
                                display: block;
                                font-size: 20px;
                                font-weight: bold;
                                color: #303133;
                                margin-bottom: 4px;
                            }

                            .stat-label {
                                font-size: 14px;
                                color: #909399;
                            }
                        }
                    }
                }

                .user-actions {
                    display: flex;
                    gap: 10px;
                }
            }
        }

        .content-tabs {
            background: #fff;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 0 20px;

            :deep(.el-tabs__header) {
                margin: 0;
            }

            :deep(.el-tabs__nav-wrap::after) {
                display: none;
            }
        }

        .content-list {
            .content-item {
                background: #fff;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 15px;
                cursor: pointer;
                transition: all 0.2s;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                &:hover {
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                }

                .content-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 12px;

                    .publish-time {
                        font-size: 12px;
                        color: #909399;
                    }
                }

                .content-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: #303133;
                    margin: 0 0 10px;
                    line-height: 1.4;
                }

                .content-text {
                    color: #606266;
                    line-height: 1.6;
                    margin-bottom: 12px;

                    // 限制显示行数
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }

                .image-gallery {
                    display: flex;
                    gap: 8px;
                    margin-bottom: 12px;

                    .gallery-image {
                        width: 80px;
                        height: 80px;
                        border-radius: 6px;
                        cursor: pointer;
                    }
                }

                .topics {
                    margin-bottom: 12px;

                    .topic-tag {
                        margin-right: 6px;
                        margin-bottom: 4px;
                        cursor: pointer;

                        &:hover {
                            background-color: #409eff;
                            color: #fff;
                        }
                    }
                }

                .interaction-stats {
                    display: flex;
                    gap: 20px;
                    color: #909399;
                    font-size: 14px;

                    .stat {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                    }
                }
            }
        }

        .load-more {
            text-align: center;
            margin: 20px 0;
        }
    }
}
</style>
