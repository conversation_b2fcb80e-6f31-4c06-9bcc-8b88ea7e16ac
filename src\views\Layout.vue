<script setup>
import {
    House,
    Search,
    Bell,
    ChatDotRound,
    User,
    Crop,
    EditPen,
    SwitchButton,
    CaretBottom,
    Plus,
    TrendCharts,
    UserFilled
} from '@element-plus/icons-vue'
import avatar from '@/assets/default.png'

//导入接口函数
import {userInfoGetService, userLogoutService} from '@/api/user.js'
//导入pinia
import {useUserInfoStore} from '@/stores/userInfo.js'
const userInfoStore = useUserInfoStore();
import {ref} from 'vue'

//获取个人信息
const getUserInfo = async ()=>{
    try {
        let result = await userInfoGetService();
        //存储pinia
        userInfoStore.setInfo(result.data)
    } catch (error) {
        console.log('获取用户信息失败:', error)
    }
}

// 检查是否已登录，如果已登录则获取用户信息
import { useTokenStore } from '@/stores/token.js'
const tokenStore = useTokenStore()
if (tokenStore.token) {
    getUserInfo();
}

import {ElMessage,ElMessageBox} from 'element-plus'
import { useRouter } from 'vue-router';
const router = useRouter();

//条目被点击后调用的函数
const handleCommand = (command) => {
    //判断指令
    if(command === 'logout'){
        //退出登录
        ElMessageBox.confirm(
            '确认退出登录吗？',
            '温馨提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
            .then(async () => {
                try {
                    // 调用登出接口
                    await userLogoutService()
                } catch (error) {
                    console.log('登出接口调用失败:', error)
                }
                //清空pinia中的token和个人信息
                userInfoStore.removeInfo()
                tokenStore.removeToken()
                //跳转到登录页
                router.push('/login')
                ElMessage({
                    type: 'success',
                    message: '退出登录成功',
                })
            })
            .catch(() => {
                //用户点击了取消
                ElMessage({
                    type: 'info',
                    message: '用户取消了退出登录',
                })
            })
    }else{
        //路由
        router.push('/user/'+command)
    }
}

// 搜索关键词
const searchKeyword = ref('')

// 搜索功能
const handleSearch = () => {
    if (searchKeyword.value.trim()) {
        router.push(`/search?q=${encodeURIComponent(searchKeyword.value)}`)
    }
}

</script>

<template>
    <div class="layout-container">
        <!-- 顶部导航栏 -->
        <el-header class="header">
            <div class="header-content">
                <!-- Logo和标题 -->
                <div class="logo-section">
                    <img src="@/assets/logo.png" alt="职言圈" class="logo" />
                    <h1 class="title">职言圈</h1>
                </div>

                <!-- 搜索框 -->
                <div class="search-section">
                    <el-input
                        v-model="searchKeyword"
                        placeholder="搜索内容、话题、用户..."
                        class="search-input"
                        @keyup.enter="handleSearch"
                    >
                        <template #prefix>
                            <el-icon><Search /></el-icon>
                        </template>
                    </el-input>
                </div>

                <!-- 右侧操作区 -->
                <div class="actions-section">
                    <el-button type="primary" :icon="Plus" @click="router.push('/create')">
                        发布
                    </el-button>

                    <el-badge :value="0" :hidden="true">
                        <el-button :icon="Bell" circle />
                    </el-badge>

                    <el-badge :value="0" :hidden="true">
                        <el-button :icon="ChatDotRound" circle />
                    </el-badge>

                    <!-- 用户头像下拉菜单 -->
                    <el-dropdown placement="bottom-end" @command="handleCommand" v-if="tokenStore.token">
                        <span class="user-dropdown">
                            <el-avatar :src="userInfoStore.info.avatarUrl || avatar" :size="32" />
                            <el-icon class="dropdown-icon">
                                <CaretBottom />
                            </el-icon>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="profile" :icon="User">个人主页</el-dropdown-item>
                                <el-dropdown-item command="info" :icon="User">基本资料</el-dropdown-item>
                                <el-dropdown-item command="avatar" :icon="Crop">更换头像</el-dropdown-item>
                                <el-dropdown-item command="reset-password" :icon="EditPen">重置密码</el-dropdown-item>
                                <el-dropdown-item divided command="logout" :icon="SwitchButton">退出登录</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>

                    <!-- 未登录时显示登录按钮 -->
                    <el-button v-else type="primary" @click="router.push('/login')">
                        登录
                    </el-button>
                </div>
            </div>
        </el-header>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 左侧导航 -->
            <el-aside class="sidebar" width="240px">
                <el-menu
                    :default-active="$route.path"
                    class="sidebar-menu"
                    router
                >
                    <el-menu-item index="/">
                        <el-icon><House /></el-icon>
                        <span>首页</span>
                    </el-menu-item>
                    <el-menu-item index="/trending">
                        <el-icon><TrendCharts /></el-icon>
                        <span>热门</span>
                    </el-menu-item>
                    <el-menu-item index="/following" v-if="tokenStore.token">
                        <el-icon><UserFilled /></el-icon>
                        <span>关注</span>
                    </el-menu-item>
                </el-menu>

                <!-- 热门话题 -->
                <div class="hot-topics" v-if="tokenStore.token">
                    <h3>热门话题</h3>
                    <div class="topic-list">
                        <!-- 这里后续会动态加载热门话题 -->
                        <div class="topic-item">#前端开发</div>
                        <div class="topic-item">#职场生活</div>
                        <div class="topic-item">#技术分享</div>
                    </div>
                </div>
            </el-aside>

            <!-- 中间内容区域 -->
            <el-main class="content">
                <router-view></router-view>
            </el-main>

            <!-- 右侧边栏 -->
            <el-aside class="right-sidebar" width="300px" v-if="tokenStore.token">
                <div class="user-card">
                    <el-avatar :src="userInfoStore.info.avatarUrl || avatar" :size="60" />
                    <div class="user-info">
                        <h3>{{ userInfoStore.info.nickname || '用户' }}</h3>
                        <p class="user-bio">{{ userInfoStore.info.bio || '这个人很懒，什么都没写...' }}</p>
                    </div>
                </div>

                <div class="stats-card">
                    <div class="stat-item">
                        <span class="stat-number">{{ userInfoStore.info.followingCount || 0 }}</span>
                        <span class="stat-label">关注</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ userInfoStore.info.followerCount || 0 }}</span>
                        <span class="stat-label">粉丝</span>
                    </div>
                </div>
            </el-aside>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.layout-container {
    min-height: 100vh;
    background-color: #f5f5f5;

    .header {
        background-color: #fff;
        border-bottom: 1px solid #e4e7ed;
        padding: 0;
        height: 60px;
        position: sticky;
        top: 0;
        z-index: 1000;

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .logo-section {
                display: flex;
                align-items: center;
                gap: 12px;

                .logo {
                    height: 32px;
                    width: auto;
                }

                .title {
                    font-size: 20px;
                    font-weight: bold;
                    color: #409eff;
                    margin: 0;
                }
            }

            .search-section {
                flex: 1;
                max-width: 400px;
                margin: 0 40px;

                .search-input {
                    width: 100%;
                }
            }

            .actions-section {
                display: flex;
                align-items: center;
                gap: 12px;

                .user-dropdown {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    padding: 4px;
                    border-radius: 6px;
                    transition: background-color 0.2s;

                    &:hover {
                        background-color: #f5f7fa;
                    }

                    .dropdown-icon {
                        margin-left: 4px;
                        color: #909399;
                        font-size: 12px;
                    }
                }
            }
        }
    }

    .main-container {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        gap: 20px;
        padding: 20px;

        .sidebar {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px 0;
            height: fit-content;
            position: sticky;
            top: 80px;

            .sidebar-menu {
                border: none;

                .el-menu-item {
                    margin: 0 12px 8px;
                    border-radius: 6px;

                    &:hover {
                        background-color: #f5f7fa;
                    }

                    &.is-active {
                        background-color: #409eff;
                        color: #fff;
                    }
                }
            }

            .hot-topics {
                margin-top: 30px;
                padding: 0 20px;

                h3 {
                    font-size: 16px;
                    margin-bottom: 15px;
                    color: #303133;
                }

                .topic-list {
                    .topic-item {
                        padding: 8px 12px;
                        margin-bottom: 6px;
                        background-color: #f5f7fa;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                        color: #606266;
                        transition: all 0.2s;

                        &:hover {
                            background-color: #409eff;
                            color: #fff;
                        }
                    }
                }
            }
        }

        .content {
            flex: 1;
            padding: 0;
        }

        .right-sidebar {
            .user-card {
                background-color: #fff;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
                text-align: center;

                .user-info {
                    margin-top: 15px;

                    h3 {
                        margin: 0 0 8px;
                        font-size: 16px;
                        color: #303133;
                    }

                    .user-bio {
                        margin: 0;
                        font-size: 14px;
                        color: #909399;
                        line-height: 1.4;
                    }
                }
            }

            .stats-card {
                background-color: #fff;
                border-radius: 8px;
                padding: 20px;
                display: flex;
                justify-content: space-around;

                .stat-item {
                    text-align: center;

                    .stat-number {
                        display: block;
                        font-size: 20px;
                        font-weight: bold;
                        color: #303133;
                        margin-bottom: 4px;
                    }

                    .stat-label {
                        font-size: 14px;
                        color: #909399;
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .layout-container {
        .header .header-content {
            padding: 0 15px;

            .search-section {
                margin: 0 20px;
                max-width: 200px;
            }
        }

        .main-container {
            padding: 15px;
            gap: 15px;

            .sidebar {
                width: 200px !important;
            }

            .right-sidebar {
                display: none;
            }
        }
    }
}
</style>