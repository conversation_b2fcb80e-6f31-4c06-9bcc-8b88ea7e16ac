import request from '@/utils/request'
// import { REPEAT_DELAY } from 'element-plus/es/directives/repeat-click';

// 注册接口
export const userRegisterService = (registerData) => {
    // registerData格式：{ email, password, nickname }
    return request.post('/user/register', registerData)
}

// 登录接口
export const userLoginService = (loginData) => {
    // loginData格式：{ email, password }
    return request.post('/user/login', loginData)
}

// 获取用户基本信息接口
export const userInfoGetService = () => {
    return request.get('/users/me')
}

// 更新用户信息
export const updateUserInfoService = (userInfo) => {
    return request.put('/users/me', userInfo)
}

// 获取指定用户公开主页
export const getUserProfileService = (userId, page = 0, size = 10) => {
    return request.get(`/users/${userId}/profile`, {
        params: { page, size }
    })
}

// 获取用户关注列表
export const getUserFollowingService = (userId, page = 0, size = 10) => {
    return request.get(`/users/${userId}/following`, {
        params: { page, size }
    })
}

// 获取用户粉丝列表
export const getUserFollowersService = (userId, page = 0, size = 10) => {
    return request.get(`/users/${userId}/followers`, {
        params: { page, size }
    })
}

// 用户登出
export const userLogoutService = () => {
    return request.post('/user/logout')
}