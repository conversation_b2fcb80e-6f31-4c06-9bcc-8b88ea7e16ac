import request from '@/utils/request'
// import { REPEAT_DELAY } from 'element-plus/es/directives/repeat-click';

// 注册接口
export const userRegisterService = (registerData) => {
    // registerData格式：{ email, password, nickname }
    return request.post('/user/register', registerData)
}

// 登录接口
export const userLoginService = (loginData) => {
    // loginData格式：{ email, password }
    return request.post('/user/login', loginData)
}

// 获取用户基本信息接口 (你的示例代码中已用到)
export const userInfoGetService = () => {
    return request.get('/users/me')
}

// ... 其他用户相关的API