<template>
    <div class="article-detail" v-loading="loading">
        <div v-if="article" class="article-container">
            <!-- 文章头部 -->
            <div class="article-header">
                <h1 class="article-title">{{ article.title }}</h1>
                
                <!-- 作者信息 -->
                <div class="author-info">
                    <el-avatar :src="article.author.avatarUrl || defaultAvatar" :size="50" />
                    <div class="author-details">
                        <div class="author-name">{{ article.author.nickname }}</div>
                        <div class="publish-info">
                            <span>{{ formatTime(article.publishTime) }}</span>
                            <span class="separator">·</span>
                            <span>阅读 {{ article.viewCount || 0 }}</span>
                        </div>
                    </div>
                    <el-button
                        v-if="tokenStore.token && article.author.userId !== userInfoStore.info.userId"
                        type="primary"
                        :loading="followLoading"
                        @click="handleFollow"
                    >
                        {{ article.author.isFollowing ? '已关注' : '关注' }}
                    </el-button>
                </div>

                <!-- 话题标签 -->
                <div v-if="article.topics && article.topics.length > 0" class="topics">
                    <el-tag
                        v-for="topic in article.topics"
                        :key="topic"
                        size="small"
                        type="info"
                        class="topic-tag"
                        @click="handleTopicClick(topic)"
                    >
                        #{{ topic }}
                    </el-tag>
                </div>
            </div>

            <!-- 文章内容 -->
            <div class="article-content">
                <!-- 封面图 -->
                <div v-if="article.coverImageUrl" class="cover-image">
                    <el-image :src="article.coverImageUrl" fit="cover" />
                </div>
                
                <!-- 正文内容 -->
                <div class="content-body" v-html="article.content"></div>
                
                <!-- 投票组件 -->
                <div v-if="article.poll" class="poll-section">
                    <div class="poll-question">{{ article.poll.question }}</div>
                    <div class="poll-options">
                        <div
                            v-for="(option, index) in article.poll.options"
                            :key="index"
                            class="poll-option"
                            :class="{ voted: article.poll.userVoted === index }"
                            @click="handleVote(index)"
                        >
                            <div class="option-text">{{ option.text }}</div>
                            <div class="option-progress">
                                <div
                                    class="progress-bar"
                                    :style="{ width: option.percentage + '%' }"
                                ></div>
                                <span class="percentage">{{ option.percentage }}%</span>
                            </div>
                        </div>
                    </div>
                    <div class="poll-stats">{{ article.poll.totalVotes }} 人参与投票</div>
                </div>
            </div>

            <!-- 互动区域 -->
            <div class="interaction-area">
                <div class="interaction-buttons">
                    <el-button
                        type="text"
                        :icon="article.isLiked ? StarFilled : Star"
                        :class="{ liked: article.isLiked }"
                        @click="handleLike"
                    >
                        {{ article.likeCount || 0 }} 点赞
                    </el-button>
                    
                    <el-button
                        type="text"
                        :icon="ChatDotRound"
                    >
                        {{ article.commentCount || 0 }} 评论
                    </el-button>
                    
                    <el-button
                        type="text"
                        :icon="article.isCollected ? StarFilled : Star"
                        :class="{ collected: article.isCollected }"
                        @click="handleCollect"
                    >
                        {{ article.isCollected ? '已收藏' : '收藏' }}
                    </el-button>
                    
                    <el-button type="text" :icon="Share">
                        分享
                    </el-button>
                </div>
            </div>

            <!-- 评论区域 -->
            <div class="comment-area">
                <h3>评论 ({{ article.commentCount || 0 }})</h3>
                <CommentSection 
                    content-type="articles" 
                    :content-id="article.id" 
                />
            </div>
        </div>

        <!-- 错误状态 -->
        <el-result
            v-else-if="!loading"
            icon="warning"
            title="文章不存在"
            sub-title="该文章可能已被删除或不存在"
        >
            <template #extra>
                <el-button type="primary" @click="$router.go(-1)">返回</el-button>
            </template>
        </el-result>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTokenStore } from '@/stores/token.js'
import { useUserInfoStore } from '@/stores/userInfo.js'
import { getArticleDetailService } from '@/api/content.js'
import { likeService, unlikeService, followUserService, unfollowUserService, collectArticleService, uncollectArticleService, voteService } from '@/api/interaction.js'
import { ElMessage } from 'element-plus'
import {
    Star,
    StarFilled,
    ChatDotRound,
    Share
} from '@element-plus/icons-vue'
import CommentSection from '@/components/CommentSection.vue'
import defaultAvatar from '@/assets/default.png'

const route = useRoute()
const router = useRouter()
const tokenStore = useTokenStore()
const userInfoStore = useUserInfoStore()

// 响应式数据
const article = ref(null)
const loading = ref(false)
const followLoading = ref(false)

// 方法
const loadArticle = async () => {
    try {
        loading.value = true
        const result = await getArticleDetailService(route.params.id)
        article.value = result.data
    } catch (error) {
        ElMessage.error('加载文章失败')
        console.error('加载文章失败:', error)
    } finally {
        loading.value = false
    }
}

const handleLike = async () => {
    if (!tokenStore.token) {
        ElMessage.warning('请先登录')
        return
    }
    
    try {
        if (article.value.isLiked) {
            await unlikeService('ARTICLE', article.value.id)
            article.value.likeCount--
        } else {
            await likeService('ARTICLE', article.value.id)
            article.value.likeCount++
        }
        article.value.isLiked = !article.value.isLiked
    } catch (error) {
        ElMessage.error('操作失败')
    }
}

const handleCollect = async () => {
    if (!tokenStore.token) {
        ElMessage.warning('请先登录')
        return
    }
    
    try {
        if (article.value.isCollected) {
            await uncollectArticleService(article.value.id)
        } else {
            await collectArticleService(article.value.id)
        }
        article.value.isCollected = !article.value.isCollected
        ElMessage.success(article.value.isCollected ? '收藏成功' : '取消收藏')
    } catch (error) {
        ElMessage.error('操作失败')
    }
}

const handleFollow = async () => {
    if (!tokenStore.token) {
        ElMessage.warning('请先登录')
        return
    }
    
    try {
        followLoading.value = true
        if (article.value.author.isFollowing) {
            await unfollowUserService(article.value.author.userId)
        } else {
            await followUserService(article.value.author.userId)
        }
        article.value.author.isFollowing = !article.value.author.isFollowing
        ElMessage.success(article.value.author.isFollowing ? '关注成功' : '取消关注')
    } catch (error) {
        ElMessage.error('操作失败')
    } finally {
        followLoading.value = false
    }
}

const handleVote = async (optionIndex) => {
    if (!tokenStore.token) {
        ElMessage.warning('请先登录')
        return
    }
    
    if (article.value.poll.userVoted !== null) {
        ElMessage.warning('您已经投过票了')
        return
    }
    
    try {
        await voteService(article.value.poll.id, optionIndex)
        article.value.poll.userVoted = optionIndex
        ElMessage.success('投票成功')
        // 重新加载文章以获取最新投票结果
        loadArticle()
    } catch (error) {
        ElMessage.error('投票失败')
    }
}

const handleTopicClick = (topic) => {
    router.push(`/topics/${topic}`)
}

const formatTime = (time) => {
    return new Date(time).toLocaleString()
}

// 生命周期
onMounted(() => {
    loadArticle()
})
</script>

<style lang="scss" scoped>
.article-detail {
    max-width: 800px;
    margin: 0 auto;

    .article-container {
        background: #fff;
        border-radius: 8px;
        padding: 40px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .article-header {
            margin-bottom: 30px;

            .article-title {
                font-size: 28px;
                font-weight: 600;
                color: #303133;
                line-height: 1.4;
                margin: 0 0 20px;
            }

            .author-info {
                display: flex;
                align-items: center;
                margin-bottom: 20px;

                .author-details {
                    flex: 1;
                    margin-left: 15px;

                    .author-name {
                        font-size: 16px;
                        font-weight: 500;
                        color: #303133;
                        margin-bottom: 4px;
                    }

                    .publish-info {
                        font-size: 14px;
                        color: #909399;

                        .separator {
                            margin: 0 8px;
                        }
                    }
                }
            }

            .topics {
                .topic-tag {
                    margin-right: 8px;
                    margin-bottom: 4px;
                    cursor: pointer;

                    &:hover {
                        background-color: #409eff;
                        color: #fff;
                    }
                }
            }
        }

        .article-content {
            .cover-image {
                margin-bottom: 30px;
                text-align: center;

                .el-image {
                    max-width: 100%;
                    border-radius: 8px;
                }
            }

            .content-body {
                font-size: 16px;
                line-height: 1.8;
                color: #303133;
                margin-bottom: 30px;

                // 文章内容样式
                :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
                    margin: 20px 0 15px;
                    color: #303133;
                }

                :deep(p) {
                    margin: 15px 0;
                }

                :deep(img) {
                    max-width: 100%;
                    height: auto;
                    border-radius: 6px;
                    margin: 15px 0;
                }

                :deep(blockquote) {
                    border-left: 4px solid #409eff;
                    padding-left: 15px;
                    margin: 20px 0;
                    color: #606266;
                    background-color: #f8f9fa;
                    padding: 15px;
                    border-radius: 4px;
                }

                :deep(code) {
                    background-color: #f5f7fa;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-family: 'Courier New', monospace;
                }

                :deep(pre) {
                    background-color: #f5f7fa;
                    padding: 15px;
                    border-radius: 6px;
                    overflow-x: auto;
                    margin: 15px 0;
                }
            }

            .poll-section {
                border: 1px solid #e4e7ed;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 30px;

                .poll-question {
                    font-size: 18px;
                    font-weight: 500;
                    margin-bottom: 15px;
                    color: #303133;
                }

                .poll-options {
                    .poll-option {
                        margin-bottom: 12px;
                        cursor: pointer;

                        &.voted {
                            .option-text {
                                color: #409eff;
                                font-weight: 500;
                            }
                        }

                        .option-text {
                            margin-bottom: 6px;
                            color: #606266;
                            font-size: 16px;
                        }

                        .option-progress {
                            position: relative;
                            height: 24px;
                            background-color: #f5f7fa;
                            border-radius: 12px;
                            overflow: hidden;

                            .progress-bar {
                                height: 100%;
                                background-color: #409eff;
                                transition: width 0.3s;
                            }

                            .percentage {
                                position: absolute;
                                right: 10px;
                                top: 50%;
                                transform: translateY(-50%);
                                font-size: 14px;
                                color: #909399;
                            }
                        }
                    }
                }

                .poll-stats {
                    font-size: 14px;
                    color: #909399;
                    margin-top: 10px;
                }
            }
        }

        .interaction-area {
            border-top: 1px solid #f0f2f5;
            border-bottom: 1px solid #f0f2f5;
            padding: 20px 0;
            margin-bottom: 30px;

            .interaction-buttons {
                display: flex;
                gap: 30px;

                .el-button {
                    padding: 0;
                    color: #909399;
                    font-size: 16px;

                    &:hover {
                        color: #409eff;
                    }

                    &.liked {
                        color: #f56c6c;
                    }

                    &.collected {
                        color: #e6a23c;
                    }
                }
            }
        }

        .comment-area {
            h3 {
                font-size: 20px;
                color: #303133;
                margin-bottom: 20px;
            }
        }
    }
}
</style>
