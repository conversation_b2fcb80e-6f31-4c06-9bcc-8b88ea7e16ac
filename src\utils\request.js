//定制请求的实例

//导入axios  npm install axios
import axios from 'axios';
import { ElMessage } from 'element-plus'
//定义一个变量,记录公共的前缀  ,  baseURL
const baseURL = '/api';
const instance = axios.create({baseURL})

//导入token状态
import { useTokenStore } from '@/stores/token.js';
//添加请求拦截器
instance.interceptors.request.use(
    (config)=>{
        //在发送请求之前做什么
        let tokenStore = useTokenStore()
        //如果token中有值，在携带
        if(tokenStore.token){
            config.headers.Authorization = `Bearer ${tokenStore.token}`
        }
        return config;
    },
    (err)=>{
        //如果请求错误做什么
        Promise.reject(err)
    }
)


// import { useRouter } from 'vue-router';
// const router = useRouter()
import router from '@/router'
//添加响应拦截器
instance.interceptors.response.use(
    result=>{
        //判断业务状态码 - 根据API文档，成功状态码为200或201
        if(result.data.code === 200 || result.data.code === 201){
            return result.data;
        }
        //操作失败
        // alert(result.data.msg?result.data.msg:'服务异常');
        //这也是一个弹出框，是element-plus的，在官网的“Message信息提示”可以查看
        ElMessage.error(result.data.message?result.data.message:'服务异常') 
        //异步操作的状态转换为失败
        return Promise.reject(result.data);
    },
    err=>{
        //判断响应状态码，如果为401，则证明未登录，提示请登录，并跳转到登录页面
        if(err.request.status===401){
            ElMessage.error('请先登录');
            router.push('/login');
        }
        ElMessage.error('服务异常');
        return Promise.reject(err);//异步的状态转化成失败的状态
    }
)

export default instance;