//导入vue-router
import { createRouter, createWebHistory } from 'vue-router'
//导入组件
import LoginVue from '@/views/Login.vue'
import LayoutVue from '@/views/Layout.vue'

//定义路由关系
const routes = [
    { path: '/login', component: LoginVue },
    { 
        path: '/', component: LayoutVue,
        // path: '/', component: LayoutVue,redirect:'/article/category', children:[
        // { path: '/article/category', component: ArticleCategory },
        // { path: '/article/manage', component: ArticleManage },
        // { path: '/user/avatar', component: UserAvatar },
        // { path: '/user/info', component: UserInfo },
        // { path: '/user/reset-password', component: UserResetPassword },
    }
]


//创建路由器
const router = createRouter({
    history: createWebHistory(),
    routes: routes
});

export default router