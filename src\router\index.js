//导入vue-router
import { createRouter, createWebHistory } from 'vue-router'
//导入组件
import LoginVue from '@/views/Login.vue'
import LayoutVue from '@/views/Layout.vue'
import HomeVue from '@/views/Home.vue'
import ArticleDetailVue from '@/views/ArticleDetail.vue'

//定义路由关系
const routes = [
    { path: '/login', component: LoginVue },
    {
        path: '/',
        component: LayoutVue,
        redirect: '/home',
        children: [
            { path: '/home', component: HomeVue },
            { path: '/articles/:id', component: ArticleDetailVue },
            // 其他子路由将在后续添加
        ]
    }
]


//创建路由器
const router = createRouter({
    history: createWebHistory(),
    routes: routes
});

// 路由守卫
import { useTokenStore } from '@/stores/token.js'

router.beforeEach((to, _from, next) => {
    const tokenStore = useTokenStore()

    // 如果访问登录页且已登录，重定向到首页
    if (to.path === '/login' && tokenStore.token) {
        next('/')
        return
    }

    // 其他路由正常访问
    next()
})

export default router